//
//  SubscriptionOfferView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import StoreKit
import SafariServices

struct SubscriptionOfferView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false

    var body: some View {
        ZStack {
            // Premium gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // Animated background particles
            ForEach(0..<8, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 20...60))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .scaleEffect(animateContent ? 1.2 : 0.8)
                    .animation(
                        Animation.easeInOut(duration: Double.random(in: 2...4))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                        value: animateContent
                    )
            }

            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { dismiss() }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)

                        // Premium crown icon
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.yellow, Color.orange],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 120)
                                .shadow(color: .yellow.opacity(0.3), radius: 20, x: 0, y: 10)

                            Image(systemName: "crown.fill")
                                .font(.system(size: 50))
                                .foregroundColor(.white)
                        }
                        .scaleEffect(animateContent ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: animateContent)

                        // Title and subtitle
                        VStack(spacing: 12) {
                            Text(NSLocalizedString("premium_title", comment: ""))
                                .font(.system(size: 32, weight: .bold, design: .rounded))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)

                            Text(NSLocalizedString("premium_subtitle", comment: ""))
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 40)

                    // Features section
                    VStack(spacing: 20) {
                        PremiumFeatureCard(
                            icon: "xmark.circle.fill",
                            title: NSLocalizedString("no_ads", comment: ""),
                            description: NSLocalizedString("no_ads_description", comment: ""),
                            gradient: [Color.red, Color.pink]
                        )

                        PremiumFeatureCard(
                            icon: "lock.open.fill",
                            title: NSLocalizedString("all_categories", comment: ""),
                            description: NSLocalizedString("all_categories_description", comment: ""),
                            gradient: [Color.green, Color.mint]
                        )

                        PremiumFeatureCard(
                            icon: "sparkles",
                            title: NSLocalizedString("premium_features", comment: ""),
                            description: NSLocalizedString("premium_features_description", comment: ""),
                            gradient: [Color.purple, Color.blue]
                        )

                        PremiumFeatureCard(
                            icon: "arrow.clockwise",
                            title: NSLocalizedString("regular_updates", comment: ""),
                            description: NSLocalizedString("regular_updates_description", comment: ""),
                            gradient: [Color.orange, Color.yellow]
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)

                    // Pricing section
                    if let subscription = subscriptionManager.availableSubscriptions.first {
                        VStack(spacing: 20) {
                            VStack(spacing: 8) {
                                Text(NSLocalizedString("free_trial_info", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)

                                Text("Then \(subscription.displayPrice)\(NSLocalizedString("per_month", comment: ""))")
                                    .font(.title.bold())
                                    .foregroundColor(.yellow)

                                Text(NSLocalizedString("cancel_anytime", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }

                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            Task {
                                await subscriptionManager.purchaseSubscription()
                                if subscriptionManager.isSubscribed {
                                    subscriptionManager.markOfferAsShown()
                                    dismiss()
                                }
                            }
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }

                                Text(subscriptionManager.purchaseState == .purchasing ? NSLocalizedString("processing", comment: "") : NSLocalizedString("start_free_trial", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: [Color.green, Color.blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(subscriptionManager.purchaseState == .purchasing ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: subscriptionManager.purchaseState)

                        // Restore purchases
                        Button(NSLocalizedString("restore_purchases", comment: "")) {
                            Task {
                                await subscriptionManager.restorePurchases()
                            }
                        }
                        .font(.body)
                        .foregroundColor(.white.opacity(0.7))
                        .underline()
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)

                    // Subscription Details (Required by Apple)
                    VStack(spacing: 8) {
                        Text(NSLocalizedString("subscription_details_title", comment: ""))
                            .font(.caption.bold())
                            .foregroundColor(.white.opacity(0.9))

                        VStack(spacing: 4) {
                            Text(NSLocalizedString("subscription_name", comment: ""))
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))

                            Text(NSLocalizedString("subscription_duration", comment: ""))
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))

                            if let product = subscriptionManager.availableSubscriptions.first {
                                Text("\(NSLocalizedString("subscription_price", comment: "")) \(product.displayPrice)")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.7))
                            }

                            Text(NSLocalizedString("subscription_auto_renewal", comment: ""))
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)

                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()

                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
}

// MARK: - Premium Feature Card Component
struct PremiumFeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let gradient: [Color]

    var body: some View {
        HStack(spacing: 20) {
            // Icon container
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                    .shadow(color: gradient.first?.opacity(0.3) ?? .clear, radius: 8, x: 0, y: 4)

                Image(systemName: icon)
                    .font(.title2.bold())
                    .foregroundColor(.white)
            }

            // Content
            VStack(alignment: .leading, spacing: 8) {
                Text(title)
                    .font(.headline.bold())
                    .foregroundColor(.white)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
            }

            Spacer()

            // Checkmark
            Image(systemName: "checkmark.circle.fill")
                .font(.title2)
                .foregroundColor(.green)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

// MARK: - Feature Row Component (Legacy)
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline.bold())
                    .foregroundColor(.white)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 15)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}



// MARK: - Safari View for Terms and Privacy
struct SafariView: UIViewControllerRepresentable {
    let url: URL

    func makeUIViewController(context: Context) -> SFSafariViewController {
        return SFSafariViewController(url: url)
    }

    func updateUIViewController(_ uiViewController: SFSafariViewController, context: Context) {}
}

#Preview {
    SubscriptionOfferView()
}
