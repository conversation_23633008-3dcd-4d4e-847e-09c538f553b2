import SwiftUI
import SafariServices

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("gameDuration") private var gameDuration = 30
    @AppStorage("language") private var language = "tr"
    @AppStorage("motionSensitivity") private var motionSensitivity = 1 // 0: <PERSON><PERSON><PERSON><PERSON><PERSON>, 1: <PERSON><PERSON>, 2: <PERSON><PERSON><PERSON><PERSON>
    @State private var showRestartAlert = false
    @State private var selectedLanguage: String?
    @State private var showSubscriptionOffer = false
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var showOnboarding = false
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var audioManager = AudioManager.shared

    let durationOptions = [20, 30, 40, 50, 60]
    let sensitivityOptions = [
        (0, NSLocalizedString("low_sensitivity", comment: ""), "🐌"),
        (1, NSLocalizedString("medium_sensitivity", comment: ""), "⚡"),
        (2, NSLocalizedString("high_sensitivity", comment: ""), "🚀")
    ]
    let languages = [
        ("ar", "العربية", "🇸🇦"),
        ("de", "Deutsch", "🇩🇪"),
        ("en", "English", "🇬🇧"),
        ("es", "Español", "🇪🇸"),
        ("fr", "Français", "🇫🇷"),
        ("hi", "हिंदी", "🇮🇳"),
        ("it", "Italiano", "🇮🇹"),
        ("ja", "日本語", "🇯🇵"),
        ("ko", "한국어", "🇰🇷"),
        ("nl", "Nederlands", "🇳🇱"),
        ("pl", "Polski", "🇵🇱"),
        ("pt", "Português", "🇵🇹"),
        ("ru", "Русский", "🇷🇺"),
        ("sv", "Svenska", "🇸🇪"),
        ("tr", "Türkçe", "🇹🇷"),
        ("zh", "中文", "🇨🇳")
    ]
    
    var currentLanguage: (String, String, String) {
        languages.first { $0.0 == language } ?? languages[0]
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Dinamik arka plan gradyanı
                themeManager.backgroundGradient
                    .ignoresSafeArea()
                    .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)

                ScrollView {
                    VStack(spacing: 30) {
                    // Tema Seçimi
                    VStack(alignment: .leading, spacing: 15) {
                        Text(NSLocalizedString("theme", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 15) {
                            ForEach(ThemeManager.AppTheme.allCases, id: \.self) { theme in
                                Button(action: {
                                    themeManager.setTheme(theme)
                                }) {
                                    VStack(spacing: 8) {
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(getThemePreview(theme))
                                            .frame(height: 40)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(
                                                        themeManager.currentTheme == theme ? Color.white : Color.clear,
                                                        lineWidth: 3
                                                    )
                                            )

                                        Text(theme.displayName)
                                            .font(.caption.bold())
                                            .foregroundColor(.white)
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(Color.white.opacity(0.2))
                    )
                    .padding(.horizontal)

                    // Subscription Yönetimi
                    VStack(alignment: .leading, spacing: 15) {
                        Text(NSLocalizedString("subscription", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        if subscriptionManager.isSubscribed {
                            // Premium kullanıcı için
                            VStack(spacing: 15) {
                                HStack {
                                    Image(systemName: "crown.fill")
                                        .foregroundColor(.yellow)
                                        .font(.title2)

                                    VStack(alignment: .leading) {
                                        Text(NSLocalizedString("premium_active", comment: ""))
                                            .font(.headline.bold())
                                            .foregroundColor(.white)

                                        Text(NSLocalizedString("premium_benefits", comment: ""))
                                            .font(.caption)
                                            .foregroundColor(.white.opacity(0.8))
                                    }

                                    Spacer()
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 15)
                                        .fill(LinearGradient(
                                            colors: [.yellow.opacity(0.3), .orange.opacity(0.3)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        ))
                                )

                                Button(action: {
                                    subscriptionManager.manageSubscriptions()
                                    AnalyticsManager.shared.logSubscriptionManageOpened()
                                }) {
                                    Text(NSLocalizedString("manage_subscription", comment: ""))
                                        .font(.body.bold())
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(
                                            RoundedRectangle(cornerRadius: 15)
                                                .fill(Color.white.opacity(0.2))
                                        )
                                }
                            }
                        } else {
                            // Free kullanıcı için
                            Button(action: {
                                showSubscriptionOffer = true
                                AnalyticsManager.shared.logSubscriptionOfferShown()
                            }) {
                                HStack {
                                    Image(systemName: "crown.fill")
                                        .foregroundColor(.yellow)
                                        .font(.title2)

                                    VStack(alignment: .leading) {
                                        Text(NSLocalizedString("upgrade_to_premium", comment: ""))
                                            .font(.headline.bold())
                                            .foregroundColor(.white)

                                        Text(NSLocalizedString("remove_ads_unlock_all", comment: ""))
                                            .font(.caption)
                                            .foregroundColor(.white.opacity(0.8))
                                    }

                                    Spacer()

                                    Image(systemName: "chevron.right")
                                        .foregroundColor(.white)
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 15)
                                        .fill(LinearGradient(
                                            colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        ))
                                )
                            }
                        }
                    }
                    .padding(.horizontal)

                    // Oyun Süresi
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString("game_duration", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        Picker(NSLocalizedString("game_duration", comment: ""), selection: $gameDuration) {
                            ForEach(durationOptions, id: \.self) { duration in
                                Text("\(duration) \(NSLocalizedString("seconds", comment: ""))")
                                    .foregroundColor(.white)
                                    .tag(duration)
                            }
                        }
                        .pickerStyle(.segmented)
                        .padding(.vertical)
                        .onChange(of: gameDuration) { newValue in
                            AnalyticsManager.shared.logGameDurationChange(newDuration: newValue)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(Color.white.opacity(0.2))
                    )
                    .padding(.horizontal)
                    
                    // Hareket Hassasiyeti
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString("motion_sensitivity", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        Menu {
                            ForEach(sensitivityOptions, id: \.0) { option in
                                Button {
                                    motionSensitivity = option.0
                                    AnalyticsManager.shared.logMotionSensitivityChange(newSensitivity: option.0)
                                } label: {
                                    Label {
                                        Text(option.1)
                                    } icon: {
                                        Text(option.2)
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(sensitivityOptions.first { $0.0 == motionSensitivity }?.2 ?? "⚡")
                                    .font(.title2)
                                Text(sensitivityOptions.first { $0.0 == motionSensitivity }?.1 ?? NSLocalizedString("medium_sensitivity", comment: ""))
                                    .font(.body)
                                    .foregroundColor(.white)
                                Spacer()
                                Image(systemName: "chevron.down")
                                    .foregroundColor(.white)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 15)
                                    .fill(Color.white.opacity(0.2))
                            )
                        }
                    }
                    .padding(.horizontal)

                    // Ses Ayarları
                    VStack(alignment: .leading, spacing: 15) {
                        Text(NSLocalizedString("audio_settings", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        // Parti Müziği
                        HStack {
                            Image(systemName: "music.note")
                                .foregroundColor(.white)
                                .frame(width: 24)

                            Text(NSLocalizedString("party_music", comment: ""))
                                .foregroundColor(.white)

                            Spacer()

                            Toggle("", isOn: $audioManager.isMusicEnabled)
                                .toggleStyle(SwitchToggleStyle(tint: .green))
                                .onChange(of: audioManager.isMusicEnabled) { _ in
                                    audioManager.toggleMusic()
                                }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(Color.white.opacity(0.2))
                        )

                        // Ses Efektleri
                        HStack {
                            Image(systemName: "speaker.wave.2")
                                .foregroundColor(.white)
                                .frame(width: 24)

                            Text(NSLocalizedString("sound_effects", comment: ""))
                                .foregroundColor(.white)

                            Spacer()

                            Toggle("", isOn: $audioManager.areSoundEffectsEnabled)
                                .toggleStyle(SwitchToggleStyle(tint: .green))
                                .onChange(of: audioManager.areSoundEffectsEnabled) { _ in
                                    audioManager.toggleSoundEffects()
                                }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(Color.white.opacity(0.2))
                        )
                    }
                    .padding(.horizontal)

                    // Nasıl Oynanır
                    VStack(alignment: .leading, spacing: 15) {
                        Text(NSLocalizedString("how_to_play", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        Button(action: {
                            showOnboarding = true
                            AnalyticsManager.shared.logOnboardingStart()
                        }) {
                            HStack {
                                Image(systemName: "questionmark.circle.fill")
                                    .foregroundColor(.white)
                                    .frame(width: 24)

                                Text(NSLocalizedString("show_tutorial", comment: ""))
                                    .foregroundColor(.white)

                                Spacer()

                                Image(systemName: "chevron.right")
                                    .foregroundColor(.white.opacity(0.6))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 15)
                                    .fill(Color.white.opacity(0.2))
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .padding(.horizontal)

                    // Dil Seçimi
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString("language", comment: ""))
                            .font(.title2.bold())
                            .foregroundColor(.white)

                        Menu {
                            ForEach(languages, id: \.0) { lang in
                                Button {
                                    selectedLanguage = lang.0
                                    showRestartAlert = true
                                    AnalyticsManager.shared.logLanguageChange(newLanguage: lang.0)
                                } label: {
                                    Label {
                                        Text(lang.1)
                                    } icon: {
                                        Text(lang.2)
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(currentLanguage.2)
                                    .font(.title2)
                                Text(currentLanguage.1)
                                    .font(.body)
                                    .foregroundColor(.white)
                                Spacer()
                                Image(systemName: "chevron.down")
                                    .foregroundColor(.white)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 15)
                                    .fill(Color.white.opacity(0.2))
                            )
                        }
                    }
                    .padding(.horizontal)

                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()

                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 20)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 20) // ScrollView için alt padding
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.title2.bold())
                            .foregroundColor(.white)
                    }
                }
                
                ToolbarItem(placement: .principal) {
                    Text(NSLocalizedString("settings", comment: ""))
                        .font(.title2.bold())
                        .foregroundColor(.white)
                }
            }
        }
        .alert(NSLocalizedString("language_change_title", comment: ""), isPresented: $showRestartAlert) {
            Button(NSLocalizedString("restart_now", comment: ""), role: .destructive) {
                if let newLanguage = selectedLanguage {
                    // Dil değişikliğini UserDefaults'a kaydet
                    UserDefaults.standard.set([newLanguage], forKey: "AppleLanguages")
                    UserDefaults.standard.synchronize()
                    
                    // Dil değişikliğini AppStorage'a kaydet
                    language = newLanguage
                    
                    // Uygulamayı yeniden başlat
                    exit(0)
                }
            }
            Button(NSLocalizedString("cancel", comment: ""), role: .cancel) {
                selectedLanguage = nil
            }
        } message: {
            Text(NSLocalizedString("language_change_message", comment: ""))
        }
        .sheet(isPresented: $showSubscriptionOffer) {
            SubscriptionOfferView()
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
        .fullScreenCover(isPresented: $showOnboarding) {
            OnboardingView()
        }
        .onAppear {
            // Dikey mod için ekran yönlendirmesini ayarla
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                let geometryPreferences = UIWindowScene.GeometryPreferences.iOS(interfaceOrientations: .portrait)
                windowScene.requestGeometryUpdate(geometryPreferences) { error in
                    if error != nil {
                        print("Ekran yönlendirmesi değiştirilirken hata oluştu: \(error.localizedDescription)")
                    }
                }
            }
        }
    }

    // Tema önizleme renkleri
    private func getThemePreview(_ theme: ThemeManager.AppTheme) -> LinearGradient {
        switch theme {
        case .dynamic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.2, green: 0.6, blue: 1.0),
                    Color(red: 0.4, green: 0.8, blue: 0.9)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .classic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.8, green: 0.1, blue: 0.1),
                    Color(red: 1.0, green: 0.8, blue: 0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .neon:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.0, green: 1.0, blue: 0.8),
                    Color(red: 0.5, green: 0.0, blue: 1.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .sunset:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 1.0, green: 0.4, blue: 0.4),
                    Color(red: 1.0, green: 0.6, blue: 0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }
}

#Preview {
    SettingsView()
}