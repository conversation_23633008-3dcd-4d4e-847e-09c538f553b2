//
//  ContentView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import GoogleMobileAds

enum AdEnvironment {
    case test
    case production
    
    var bannerAdUnitID: String {
        switch self {
        case .test:
            return "ca-app-pub-3940256099942544/2934735716"
        case .production:
            return "ca-app-pub-2035219887278367/4046796456"
        }
    }
    
    var categoryBannerAdUnitID: String {
        switch self {
        case .test:
            return "ca-app-pub-3940256099942544/2934735716"
        case .production:
            return "ca-app-pub-2035219887278367/4810882080"
        }
    }
    
    var rewardedAdUnitID: String {
        switch self {
        case .test:
            return "ca-app-pub-3940256099942544/1712485313" // Test reklam kimliği
        case .production:
            return "ca-app-pub-2035219887278367/5781345646" // Gerçek reklam kimliği (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i)
        }
    }
    
    static var current: AdEnvironment {
        #if DEBUG
        return .test
        #else
        return .production
        #endif
    }
}

struct ContentView: View {
    @State private var showingCategories = false
    @State private var showSettings = false
    @State private var showSubscriptionOffer = false
    @State private var showOnboarding = false
    @State private var gameDuration = 120
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var onboardingManager = OnboardingManager.shared
    @State private var sparkleOffset: CGFloat = -250

    let durationOptions = [60, 120, 180, 240]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Dinamik arka plan gradyanı
                themeManager.backgroundGradient
                    .ignoresSafeArea()
                    .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)

                // Gelişmiş animasyonlu arka plan desenleri
                ForEach(0..<8) { index in
                    Circle()
                        .fill(themeManager.particleColors.randomElement() ?? Color.white.opacity(0.1))
                        .frame(width: CGFloat.random(in: 30...120))
                        .position(
                            x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                            y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                        )
                        .animation(
                            Animation.easeInOut(duration: Double.random(in: 3...6))
                                .repeatForever(autoreverses: true)
                                .delay(Double.random(in: 0...2)),
                            value: UUID()
                        )
                }

                // Ek dekoratif elementler
                ForEach(0..<3) { index in
                    RoundedRectangle(cornerRadius: 20)
                        .fill(themeManager.particleColors.randomElement() ?? Color.white.opacity(0.05))
                        .frame(width: CGFloat.random(in: 100...200), height: CGFloat.random(in: 20...40))
                        .rotationEffect(.degrees(Double.random(in: 0...360)))
                        .position(
                            x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                            y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                        )
                        .animation(
                            Animation.linear(duration: Double.random(in: 8...15))
                                .repeatForever(autoreverses: false),
                            value: UUID()
                        )
                }
                
                VStack(spacing: 30) {
                    // Animasyonlu Logo ve başlık
                    VStack(spacing: 25) {
                        Image(systemName: "gamecontroller.fill")
                            .font(.system(size: 80))
                            .foregroundColor(.white)
                            .padding()
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                            )
                            .bounceIn()
                            .pulse()

                        HStack(spacing: 0) {
                            ForEach(Array("GuessUp!".enumerated()), id: \.offset) { index, character in
                                Text(String(character))
                                    .font(.system(size: 48, weight: .bold, design: .rounded))
                                    .foregroundStyle(
                                        LinearGradient(
                                            gradient: Gradient(colors: [.white, .yellow, .orange]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: .black.opacity(0.5), radius: 8, x: 0, y: 4)
                                    .scaleEffect(1.0)
                                    .animation(
                                        .spring(response: 0.6, dampingFraction: 0.8)
                                        .delay(Double(index) * 0.1),
                                        value: themeManager.currentTheme
                                    )
                            }
                        }
                        .slideIn(from: .top)
                    }
                    .padding(.top, 50)
                    
                    Spacer()
                    
                    // Animasyonlu Butonlar
                    VStack(spacing: 25) {
                        // New Game Butonu
                        NavigationLink(destination: CategoryView()) {
                            ZStack {
                                // Ana buton arka planı
                                RoundedRectangle(cornerRadius: 32)
                                    .fill(themeManager.primaryButtonGradient)
                                    .frame(width: 250, height: 70)
                                    .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)

                                // Sparkle efekti - Daha doğal animasyon
                                RoundedRectangle(cornerRadius: 32)
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color.clear,
                                                Color.white.opacity(0.6),
                                                Color.white.opacity(0.9),
                                                Color.white.opacity(0.6),
                                                Color.clear
                                            ]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(width: 80, height: 70)
                                    .offset(x: sparkleOffset)
                                    .mask(
                                        RoundedRectangle(cornerRadius: 32)
                                            .frame(width: 250, height: 70)
                                    )
                                    .onAppear {
                                        // Daha doğal ve smooth animasyon
                                        withAnimation(
                                            .easeOut(duration: 1.2)
                                            .repeatForever(autoreverses: false)
                                            .delay(0.5)
                                        ) {
                                            sparkleOffset = 220
                                        }
                                    }

                                // Buton metni
                                Text(NSLocalizedString("new_game", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                        .bounceIn()
                        .onTapGesture {
                            AnimationManager.shared.playButtonTapHaptic()
                            AnalyticsManager.shared.logGameStart(category: "main_menu")
                        }

                        AnimatedButton(
                            title: NSLocalizedString("settings", comment: ""),
                            action: {
                                showSettings = true
                                AnalyticsManager.shared.logSettingsOpen()
                            },
                            style: .glow,
                            gradient: themeManager.secondaryButtonGradient
                        )
                        .slideIn(from: .bottom)
                    }
                    .padding(.bottom, 20)
                    
                    // Banner Reklamı - Sadece subscription yoksa göster
                    if !subscriptionManager.isSubscribed {
                        AdBannerView(adUnitID: AdEnvironment.current.bannerAdUnitID)
                            .frame(height: 50)
                            .padding(.bottom, 10)
                    }
                }
            }
        }
        .navigationViewStyle(.stack)
        .sheet(isPresented: $showSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showSubscriptionOffer) {
            SubscriptionOfferView()
        }
        .fullScreenCover(isPresented: $showOnboarding) {
            OnboardingView(onCompletion: {
                // Onboarding tamamlandığında subscription offer'ı göster
                if !subscriptionManager.hasShownOffer && !subscriptionManager.isSubscribed {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        showSubscriptionOffer = true
                        AnalyticsManager.shared.logSubscriptionOfferShown()
                    }
                }
            })
        }
        .onAppear {
            AnalyticsManager.shared.logAppOpen()

            // Onboarding'i göster (ilk açılışta)
            if onboardingManager.shouldShowOnboarding {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showOnboarding = true
                }
            }
            // Subscription offer'ı göster (sadece onboarding tamamlanmışsa ve daha önce gösterilmemişse)
            else if !subscriptionManager.hasShownOffer && !subscriptionManager.isSubscribed {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    showSubscriptionOffer = true
                    AnalyticsManager.shared.logSubscriptionOfferShown()
                }
            }

            // Dikey mod için ekran yönlendirmesini ayarla
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                let geometryPreferences = UIWindowScene.GeometryPreferences.iOS(interfaceOrientations: .portrait)
                windowScene.requestGeometryUpdate(geometryPreferences) { error in
                    if error != nil {
                        print("Ekran yönlendirmesi değiştirilirken hata oluştu: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
}

// Özel buton stili - basma efekti
struct PressedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    ContentView()
}
