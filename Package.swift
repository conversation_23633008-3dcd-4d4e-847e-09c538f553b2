// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "GuessUpApp",
    platforms: [
        .iOS(.v17)
    ],
    products: [
        .library(
            name: "GuessUpA<PERSON>",
            targets: ["GuessUpApp"]),
    ],
    dependencies: [
        .package(url: "https://github.com/firebase/firebase-ios-sdk.git", from: "10.0.0"),
        .package(url: "https://github.com/googleads/swift-package-manager-google-mobile-ads.git", from: "10.0.0")
    ],
    targets: [
        .target(
            name: "GuessUpApp",
            dependencies: [
                .product(name: "FirebaseAnalytics", package: "firebase-ios-sdk"),
                .product(name: "GoogleMobileAds", package: "swift-package-manager-google-mobile-ads")
            ]),
        .testTarget(
            name: "GuessUpAppTests",
            dependencies: ["GuessUpApp"]),
    ]
) 